import { dialog, ipcMain, screen, shell } from 'electron'
import fs from 'fs-extra'

export default (mainWindow) => {
  ipcMain.handle(
    'show-open-dialog',
    async (_, { preset = '', ...options } = {}) => {
      const res = await dialog
        .showOpenDialog(options)
        .catch(e => console.warn(e))

      if (res.canceled) {
        throw new Error('User cancel operation')
      }

      if (!res.filePaths.length) {
        throw new Error('Get the directory or file path failure')
      }

      const filePaths = res.filePaths

      switch (preset) {
        case 'replaceFile':
          await fs.copy(filePaths[0], options.filePath, { overwrite: true })
          break
      }

      return filePaths
    },
  )

  ipcMain.handle('open-path', async (_, pathValue) => {
    return shell.openPath(pathValue)
  })

  ipcMain.handle('show-item-in-folder', async (_, filePath) => {
    return shell.showItemInFolder(filePath)
  })

  ipcMain.handle(
    'show-save-dialog',
    async (_, { filePath = '', ...options } = {}) => {
      const res = await dialog
        .showSaveDialog({
          ...options,
        })
        .catch(e => console.warn(e))

      if (res.canceled) {
        throw new Error('User cancel operation')
      }

      if (!res.filePath) {
        throw new Error('Failure to obtain the file path')
      }

      const destinationPath = res.filePath

      await fs.copy(filePath, destinationPath)
    },
  )

  // 获取屏幕信息
  ipcMain.handle('get-primary-display', async () => {
    try {
      const display = screen.getPrimaryDisplay()
      return {
        bounds: display.bounds,
        workArea: display.workArea,
        workAreaSize: display.workAreaSize,
        size: display.size,
        scaleFactor: display.scaleFactor,
      }
    }
    catch (error) {
      console.error('Failed to get primary display:', error)
      throw error
    }
  })

  // 获取所有显示器信息
  ipcMain.handle('get-all-displays', async () => {
    try {
      return screen.getAllDisplays().map(display => ({
        id: display.id,
        bounds: display.bounds,
        workArea: display.workArea,
        workAreaSize: display.workAreaSize,
        size: display.size,
        scaleFactor: display.scaleFactor,
        primary: display === screen.getPrimaryDisplay(),
      }))
    }
    catch (error) {
      console.error('Failed to get all displays:', error)
      throw error
    }
  })
}
